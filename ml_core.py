import numpy as np
import pandas as pd
import torch
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
from data_handler import normalize_data, create_sequences, introduce_missingness

# Import simplified deep learning models
try:
    from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet
    DEEP_MODELS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Deep learning models not available: {e}")
    DEEP_MODELS_AVAILABLE = False
    SimpleAutoencoder = None
    SimpleUNet = None

# --- PLUG-AND-PLAY MODEL REGISTRY ---
MODEL_REGISTRY = {
    'xgboost': {
        'name': 'XGBoost',
        'model_class': XGBRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.05, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': 6, 'min': 3, 'max': 15, 'prompt': "Max tree depth"},
            'subsample': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Subsample ratio"},
            'colsample_bytree': {'type': float, 'default': 1.0, 'min': 0.5, 'max': 1.0, 'prompt': "Column sample per tree"},
            'reg_alpha': {'type': float, 'default': 0, 'min': 0, 'max': 10, 'prompt': "L1 regularization"},
            'reg_lambda': {'type': float, 'default': 1, 'min': 0, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'tree_method',
            'gpu_value': 'gpu_hist',
            'cpu_value': 'hist'
        }
    },
    'lightgbm': {
        'name': 'LightGBM',
        'model_class': LGBMRegressor,
        'hyperparameters': {
            'n_estimators': {'type': int, 'default': 300, 'min': 50, 'max': 2000, 'prompt': "Number of estimators"},
            'learning_rate': {'type': float, 'default': 0.1, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'max_depth': {'type': int, 'default': -1, 'min': -1, 'max': 15, 'prompt': "Max tree depth (-1 no limit)"},
            'min_child_samples': {'type': int, 'default': 20, 'min': 5, 'max': 100, 'prompt': "Min child samples"},
        },
        'fixed_params': {'random_state': 42},
        'gpu_check': {
            'param': 'device',
            'gpu_value': 'gpu',
            'cpu_value': 'cpu'
        }
    },
    'catboost': {
        'name': 'CatBoost',
        'model_class': CatBoostRegressor,
        'hyperparameters': {
            'iterations': {'type': int, 'default': 1000, 'min': 100, 'max': 5000, 'prompt': "Iterations"},
            'learning_rate': {'type': float, 'default': None, 'min': 0.01, 'max': 0.3, 'prompt': "Learning rate"},
            'depth': {'type': int, 'default': 6, 'min': 3, 'max': 12, 'prompt': "Tree depth"},
            'l2_leaf_reg': {'type': float, 'default': 3, 'min': 1, 'max': 10, 'prompt': "L2 regularization"},
        },
        'fixed_params': {'random_state': 42, 'verbose': 0, 'early_stopping_rounds': 50},
        'gpu_check': {
            'param': 'task_type',
            'gpu_value': 'GPU',
            'cpu_value': 'CPU'
        }
    },
    'autoencoder': {
        'name': 'Autoencoder',
        'model_class': SimpleAutoencoder if DEEP_MODELS_AVAILABLE else None,
        'type': 'deep',
        'hyperparameters': {
            'sequence_len': {'type': int, 'default': 64},
            'n_features': {'type': int, 'default': 4},
            'encoding_dim': {'type': int, 'default': 32},
            'epochs': {'type': int, 'default': 50},
            'batch_size': {'type': int, 'default': 32},
        },
        'fixed_params': {}
    },
    'unet': {
        'name': 'U-Net',
        'model_class': SimpleUNet if DEEP_MODELS_AVAILABLE else None,
        'type': 'deep',
        'hyperparameters': {
            'sequence_len': {'type': int, 'default': 64},
            'n_features': {'type': int, 'default': 4},
            'epochs': {'type': int, 'default': 50},
            'batch_size': {'type': int, 'default': 32},
        },
        'fixed_params': {}
    }
}

def evaluate_model_comprehensive(model, X_train, y_train, X_val, y_val):
    """Return MAE, RMSE, R2 and a composite score."""
    y_pred = model.predict(X_val)
    mae = mean_absolute_error(y_val, y_pred)
    rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    r2 = r2_score(y_val, y_pred)
    r2_penalty = (1 - r2) if r2 > 0 else 10
    composite = (mae * 0.5) + (rmse * 0.3) + (r2_penalty * 0.2)
    return {'mae': mae, 'rmse': rmse, 'r2': r2, 'composite_score': composite}

def impute_logs(df, feature_cols, target_col, models_to_run, well_cfg, prediction_mode):
    """Main imputation routine."""
    res = df.copy()
    feat_set = feature_cols + ['MD']
    print(f'--- Target Log: {target_col} ---')

    # Train data selection
    if well_cfg['mode'] == 'separated':
        train_df = res[res['WELL'].isin(well_cfg['training_wells'])]
    else:
        train_df = res
    train = train_df[train_df[target_col].notna()].copy()

    if train.empty:
        print("No training data.")
        return res, {}

    X = train[feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    y = train[target_col]
    X_tr, X_val, y_tr, y_val = train_test_split(X, y, test_size=0.25, random_state=42)

    evals, trained = [], {}
    for name, model in models_to_run.items():
        try:
            if 'early_stopping_rounds' in model.get_params():
                model.fit(X_tr, y_tr, eval_set=[(X_val, y_val)], verbose=False)
            else:
                model.fit(X_tr, y_tr)
            ev = evaluate_model_comprehensive(model, X_tr, y_tr, X_val, y_val)
            ev['model_name'] = name
            evals.append(ev)
            trained[name] = model
            print(f'{name}: MAE={ev["mae"]:.3f}, R2={ev["r2"]:.3f}')
        except Exception as e:
            print(f'{name} failed: {e}')

    if not evals:
        print("All models failed.")
        return res, {}

    evals.sort(key=lambda x: x['composite_score'])
    best_name = evals[0]['model_name']
    best_model = trained[best_name]
    print(f'Best model: {best_name}')

    # Prediction
    if well_cfg['mode'] == 'separated':
        pred_mask = res['WELL'].isin(well_cfg['prediction_wells'])
    else:
        pred_mask = pd.Series(True, index=res.index)

    X_pred = res.loc[pred_mask, feat_set].apply(lambda c: c.fillna(c.mean()), axis=0)
    preds = best_model.predict(X_pred) if not X_pred.empty else np.array([])

    full_pred = pd.Series(np.nan, index=res.index)
    full_pred.loc[pred_mask] = preds

    imp_col = f'{target_col}_imputed'
    pred_col = f'{target_col}_pred'
    err_col = f'{target_col}_error'

    res[pred_col] = full_pred
    if prediction_mode == 3:
        res[imp_col] = full_pred
    else:
        res[imp_col] = res[target_col].fillna(full_pred)

    mask_orig = res[target_col].notna() & res[pred_col].notna()
    res[err_col] = np.nan
    res.loc[mask_orig, err_col] = np.abs(res.loc[mask_orig, target_col] - res.loc[mask_orig, pred_col])

    return res, {'target': target_col, 'evaluations': evals, 'best_model_name': best_name, 'trained_models': trained}

def impute_logs_deep(df, feature_cols, target_col, model_config, hparams):
    """Main imputation routine for a single deep learning model."""
    print(f"--- Running Deep Learning Model: {model_config['name']} ---")

    # 1. Data Preparation
    # For imputation, we typically use all features to provide context.
    all_features = feature_cols + [target_col]

    # Normalize data first
    df_scaled, scalers = normalize_data(df, all_features)

    # Create sequences from the complete, scaled data
    sequences = create_sequences(df_scaled, 'WELL', all_features, sequence_len=hparams['sequence_len'])

    # Create a training set by introducing artificial missing values
    train_sequences = introduce_missingness(sequences, missing_rate=0.2)

    # Convert to PyTorch tensors
    train_tensor = torch.from_numpy(train_sequences.astype(np.float32))
    truth_tensor = torch.from_numpy(sequences.astype(np.float32))

    # 2. Model Initialization
    hparams['n_features'] = len(all_features)  # Set n_features dynamically

    if not DEEP_MODELS_AVAILABLE or model_config['model_class'] is None:
        print("Deep learning models not available. Using placeholder.")
        # Create placeholder results
        res_df = df.copy()
        imp_col = f'{target_col}_imputed'
        pred_col = f'{target_col}_pred'
        err_col = f'{target_col}_error'

        res_df[pred_col] = res_df[target_col]
        res_df[imp_col] = res_df[target_col].fillna(res_df[target_col].mean())
        res_df[err_col] = 0.0

        eval_results = {'mae': 0.0, 'r2': 0.0, 'model_name': model_config['name'], 'composite_score': 0.0}
        return res_df, {'target': target_col, 'evaluations': [eval_results], 'best_model_name': model_config['name'], 'trained_models': {}}

    print(f"Model configuration: {model_config['name']}")
    print(f"Hyperparameters: {hparams}")
    print(f"Training data shape: {train_tensor.shape}")
    print(f"Truth data shape: {truth_tensor.shape}")

    # Create the model
    model = model_config['model_class'](**hparams)

    # 3. Training
    print("Training phase...")
    model.fit(train_tensor, truth_tensor, epochs=hparams['epochs'], batch_size=hparams['batch_size'])

    # 4. Imputation (Prediction)
    print("Prediction phase...")
    imputed_sequences_tensor = model.predict(train_tensor)

    # 5. Post-processing (placeholder)
    # For now, return the original dataframe with placeholder results
    res_df = df.copy()

    # Create placeholder imputed columns
    imp_col = f'{target_col}_imputed'
    pred_col = f'{target_col}_pred'
    err_col = f'{target_col}_error'

    # For demonstration, just copy the original values
    res_df[pred_col] = res_df[target_col]
    res_df[imp_col] = res_df[target_col].fillna(res_df[target_col].mean())
    res_df[err_col] = 0.0

    print(f"✅ {model_config['name']} imputation complete (placeholder).")

    # Create a results dictionary similar to the shallow model one
    eval_results = {'mae': 0.0, 'r2': 0.0, 'model_name': model_config['name'], 'composite_score': 0.0}

    return res_df, {'target': target_col, 'evaluations': [eval_results], 'best_model_name': model_config['name'], 'trained_models': {}}
